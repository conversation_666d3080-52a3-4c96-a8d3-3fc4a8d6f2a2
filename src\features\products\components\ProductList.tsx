import React, { useState, useMemo } from 'react'
import { Card, CardContent } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { GridIcon, ListIcon, SearchIcon, EditIcon, StockIcon, ArrowLeftIcon, ArrowRightIcon } from '../../../assets/icons'
import { useDebounce } from '../../../hooks/useDebounce'

/**
 * Ürün Listesi Bileşeni
 * 
 * Bu bileşen:
 * - Figma tasarımına sadık ürün kartları gösterir
 * - Grid/List görünüm seçenekleri sunar
 * - Arama ve filtreleme özelliği sağlar
 * - Pagination ile sayfalama yapar
 * - Touch-friendly tasarım (44px minimum)
 * - Responsive layout
 */

interface Product {
  id: string
  name: string
  description: string
  basePrice: string
  image?: string
  category: {
    name: string
    color?: string
  }
  available: boolean
  stock?: number
}

interface ProductListProps {
  searchQuery?: string
  onProductEdit?: (productId: string) => void
  onProductDelete?: (productId: string) => void
}

// Mock data - gerçek API'den gelecek
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Avocado Toast',
    description: 'Fresh avocado on sourdough bread with cherry tomatoes',
    basePrice: '8.99',
    image: '/api/placeholder/283/192',
    category: { name: 'Breakfast', color: '#025cca' },
    available: true,
    stock: 15
  },
  {
    id: '2',
    name: 'Turkey Club Sandwich',
    description: 'Triple-decker sandwich with turkey, bacon, lettuce and tomato',
    basePrice: '12.99',
    image: '/api/placeholder/283/192',
    category: { name: 'Lunch', color: '#025cca' },
    available: true,
    stock: 25
  },
  // Daha fazla mock data eklenebilir
]

export function ProductList({ searchQuery = '', onProductEdit, onProductDelete }: ProductListProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [localSearch, setLocalSearch] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [categoryFilter, setCategoryFilter] = useState<string>('')
  
  // 300ms debounce for search (kural.md'deki "Search = 300ms debounce")
  const debouncedSearch = useDebounce(localSearch || searchQuery, 300)
  
  const itemsPerPage = 8
  
  // Filtrelenmiş ürünler
  const filteredProducts = useMemo(() => {
    return mockProducts.filter(product => {
      const matchesSearch = !debouncedSearch || 
        product.name.toLowerCase().includes(debouncedSearch.toLowerCase()) ||
        product.description.toLowerCase().includes(debouncedSearch.toLowerCase())
      
      const matchesCategory = !categoryFilter || product.category.name === categoryFilter
      
      return matchesSearch && matchesCategory
    })
  }, [debouncedSearch, categoryFilter])
  
  // Sayfalama
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage)
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="space-y-6">
      {/* Header with Search and View Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-[#202325] mb-1">Ürünler</h2>
          <p className="text-sm text-[#636566]">{filteredProducts.length} ürün bulundu</p>
        </div>
        
        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="bg-neutral-100 rounded-xl p-1 flex">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="h-9 w-9 p-0 rounded-lg"
            >
              <GridIcon className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-9 w-9 p-0 rounded-lg"
            >
              <ListIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Local Search Bar */}
      <div className="relative w-full max-w-md">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <SearchIcon className="w-5 h-5 text-gray-400" />
        </div>
        <Input
          type="text"
          placeholder="Ürün ara..."
          value={localSearch}
          onChange={(e) => setLocalSearch(e.target.value)}
          className="pl-10 h-10 rounded-xl border-gray-200"
        />
      </div>

      {/* Products Grid/List */}
      {paginatedProducts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg mb-4">Ürün bulunamadı</p>
          <p className="text-gray-400">Arama kriterlerinizi değiştirmeyi deneyin</p>
        </div>
      ) : (
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' 
          : 'space-y-4'
        }>
          {paginatedProducts.map((product) => (
            <ProductCard
              key={product.id}
              product={product}
              viewMode={viewMode}
              onEdit={() => onProductEdit?.(product.id)}
              onDelete={() => onProductDelete?.(product.id)}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={filteredProducts.length}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  )
}

// Product Card Component
interface ProductCardProps {
  product: Product
  viewMode: 'grid' | 'list'
  onEdit: () => void
  onDelete: () => void
}

function ProductCard({ product, viewMode, onEdit, onDelete }: ProductCardProps) {
  if (viewMode === 'list') {
    return (
      <Card className="p-4">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0">
            {product.image && (
              <img 
                src={product.image} 
                alt={product.name}
                className="w-full h-full object-cover rounded-lg"
              />
            )}
          </div>
          <div className="flex-1">
            <h3 className="font-bold text-[#202325] text-lg">{product.name}</h3>
            <p className="text-[#636566] text-sm line-clamp-1">{product.description}</p>
            <div className="flex items-center gap-2 mt-1">
              <span className="font-bold text-[#202325] text-xl">${product.basePrice}</span>
              {product.stock && (
                <div className="flex items-center gap-1 text-[#636566] text-sm">
                  <StockIcon className="w-4 h-4" />
                  <span>{product.stock} stok</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
              className="bg-[#f0f8ff] text-[#025cca] border-none hover:bg-[#e6f3ff] h-10 px-4"
            >
              Düzenle
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onDelete}
              className="text-[#ee4e4f] hover:bg-red-50 h-10 px-3"
            >
              Sil
            </Button>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card className="overflow-hidden rounded-3xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
      {/* Product Image */}
      <div className="relative h-48 bg-gray-200">
        {product.image && (
          <img 
            src={product.image} 
            alt={product.name}
            className="w-full h-full object-cover"
          />
        )}
        
        {/* Category Badge */}
        <div className="absolute top-3 left-3">
          <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
            <span className="text-[#025cca] text-xs font-semibold">{product.category.name}</span>
          </div>
        </div>
        
        {/* Stock Badge */}
        {product.stock && (
          <div className="absolute top-3 right-3">
            <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center gap-1">
              <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
              <span className="text-[#202325] text-xs font-medium">{product.stock}</span>
            </div>
          </div>
        )}
      </div>

      {/* Product Info */}
      <CardContent className="p-5">
        <h3 className="font-bold text-[#202325] text-lg mb-2 line-clamp-1">{product.name}</h3>
        <p className="text-[#636566] text-sm mb-4 line-clamp-2 leading-relaxed">{product.description}</p>
        
        <div className="flex items-center justify-between mb-4">
          <span className="font-bold text-[#202325] text-2xl">${product.basePrice}</span>
          {product.stock && (
            <div className="flex items-center gap-1 text-[#636566] text-sm">
              <StockIcon className="w-4 h-4" />
              <span>{product.stock} stok</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={onEdit}
            className="flex-1 bg-[#f0f8ff] text-[#025cca] border-none hover:bg-[#e6f3ff] h-10 font-semibold"
            variant="outline"
          >
            Düzenle
          </Button>
          <Button
            onClick={onDelete}
            variant="outline"
            className="text-[#ee4e4f] hover:bg-red-50 h-10 px-3 font-semibold"
          >
            Sil
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Pagination Controls Component
interface PaginationControlsProps {
  currentPage: number
  totalPages: number
  totalItems: number
  onPageChange: (page: number) => void
}

function PaginationControls({ currentPage, totalPages, totalItems, onPageChange }: PaginationControlsProps) {
  return (
    <Card className="p-4 rounded-2xl">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-[#636566]">
          <span>Toplam {totalItems} ürün</span>
          <div className="w-1 h-1 bg-[#e8e8e8] rounded-full"></div>
          <span>Sayfa {currentPage} / {totalPages}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="h-10 w-10 p-0 rounded-xl"
          >
            <ArrowLeftIcon className="w-4 h-4" />
          </Button>
          
          <Button
            className="h-10 w-10 p-0 rounded-xl bg-[#025cca] text-white font-semibold"
          >
            {currentPage}
          </Button>
          
          {currentPage < totalPages && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              className="h-10 w-10 p-0 rounded-xl text-[#636566] font-semibold"
            >
              {currentPage + 1}
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="h-10 w-10 p-0 rounded-xl"
          >
            <ArrowRightIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  )
}
