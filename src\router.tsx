import { createRouter, createRoute, createRootRoute, Outlet } from '@tanstack/react-router'
import React from 'react'
import { PinLoginPage } from './features/auth/pages/PinLoginPage'
import { DashboardPage } from './features/dashboard/pages/DashboardPage'
import { ProductManagementPage } from './features/products/pages/ProductManagementPage'
import { useAuthStore } from './features/auth/stores/authStore'

/**
 * ATROPOS Restaurant POS Router Konfigürasyonu
 * 
 * Bu dosya:
 * - TanStack Router ile route tanımlamaları
 * - Authentication guard'ları
 * - Nested routing yapısı
 * - URL parametreleri ve query string yönetimi
 */

// Root route - tüm route'ların ana container'ı
const rootRoute = createRootRoute({
  component: () => {
    const { isAuthenticated } = useAuthStore()
    
    if (!isAuthenticated) {
      return <PinLoginPage />
    }
    
    return <Outlet />
  },
})

// Dashboard route
const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: DashboardPage,
})

// Products management route
const productsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/products',
  component: ProductManagementPage,
  validateSearch: (search: Record<string, unknown>) => {
    return {
      tab: (search.tab as string) || 'products',
      search: search.search as string,
      category: search.category as string,
      page: Number(search.page) || 1,
    }
  },
})

// Route tree
const routeTree = rootRoute.addChildren([
  dashboardRoute,
  productsRoute,
])

// Router instance
export const router = createRouter({ 
  routeTree,
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 0,
})

// Router type declaration for TypeScript
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}
