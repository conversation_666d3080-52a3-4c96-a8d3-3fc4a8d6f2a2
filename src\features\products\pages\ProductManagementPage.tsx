import React, { useState } from 'react'
import { useSearch, useNavigate, Link } from '@tanstack/react-router'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { ProductIcon, SearchIcon, PlusIcon, ArrowLeftIcon } from '../../../assets/icons'
import { ProductList } from '../components/ProductList'
import { CategoryList } from '../components/CategoryList'
import { ModifierGroupList } from '../components/ModifierGroupList'
import { InventoryItemList } from '../components/InventoryItemList'
import { RecipeList } from '../components/RecipeList'
import { PricingRuleList } from '../components/PricingRuleList'

/**
 * <PERSON><PERSON><PERSON>n <PERSON>netimi <PERSON>sı
 * 
 * Bu sayfa:
 * - <PERSON>gma tasarımına sadık kalarak ürün yönetim<PERSON>
 * - 5 sekme içerir: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Envanter, Fiyatlandırma
 * - Responsive tasarım ve touch-friendly UI
 * - Arama çubuğu ve yeni ekleme butonları
 * - Full viewport coverage (margin/padding sıfır)
 */
export function ProductManagementPage() {
  const search = useSearch({ from: '/products' })
  const navigate = useNavigate({ from: '/products' })

  const activeTab = search.tab || 'products'
  const searchQuery = search.search || ''

  const handleTabChange = (tab: string) => {
    navigate({
      search: (prev) => ({ ...prev, tab }),
    })
  }

  const handleSearchChange = (query: string) => {
    navigate({
      search: (prev) => ({ ...prev, search: query || undefined }),
    })
  }

  return (
    <div className="min-h-screen bg-neutral-100 w-full">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 px-6 py-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] rounded-2xl p-3 shadow-lg">
            <ProductIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-[#202325] leading-8">
              Ürün Yönetimi
            </h1>
            <p className="text-sm text-[#636566] leading-5 mt-1">
              Menü öğelerinizi yönetin ve düzenleyin
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="flex justify-end">
          <div className="relative w-80">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon className="w-5 h-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Ürün, kategori veya modifiyer ara..."
              value={searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10 h-12 rounded-2xl border-gray-200 bg-white"
            />
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white border-b border-gray-200">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="h-auto bg-transparent p-0 gap-0 ml-6">
            <TabsTrigger 
              value="products" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm"
            >
              Ürünler
            </TabsTrigger>
            <TabsTrigger 
              value="categories" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Kategoriler
            </TabsTrigger>
            <TabsTrigger 
              value="modifiers" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Modifiyerler
            </TabsTrigger>
            <TabsTrigger 
              value="inventory" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Envanter
            </TabsTrigger>
            <TabsTrigger 
              value="pricing" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Fiyatlandırma
            </TabsTrigger>
          </TabsList>

          {/* Tab Contents */}
          <TabsContent value="products" className="mt-0">
            <ProductsTabContent />
          </TabsContent>
          
          <TabsContent value="categories" className="mt-0">
            <CategoriesTabContent />
          </TabsContent>
          
          <TabsContent value="modifiers" className="mt-0">
            <ModifiersTabContent />
          </TabsContent>
          
          <TabsContent value="inventory" className="mt-0">
            <InventoryTabContent />
          </TabsContent>
          
          <TabsContent value="pricing" className="mt-0">
            <PricingTabContent />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

// Placeholder components for tab contents
function ProductsTabContent() {
  const [searchQuery, setSearchQuery] = useState('')

  const handleProductEdit = (productId: string) => {
    console.log('Edit product:', productId)
    // TODO: Modal açma işlemi
  }

  const handleProductDelete = (productId: string) => {
    console.log('Delete product:', productId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewProduct = () => {
    console.log('New product')
    // TODO: Yeni ürün modal açma
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <Button
          onClick={handleNewProduct}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Ürün Ekle
        </Button>
      </div>

      <ProductList
        searchQuery={searchQuery}
        onProductEdit={handleProductEdit}
        onProductDelete={handleProductDelete}
      />
    </div>
  )
}

function CategoriesTabContent() {
  const handleCategoryEdit = (categoryId: string) => {
    console.log('Edit category:', categoryId)
    // TODO: Modal açma işlemi
  }

  const handleCategoryDelete = (categoryId: string) => {
    console.log('Delete category:', categoryId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewCategory = () => {
    console.log('New category')
    // TODO: Yeni kategori modal açma
  }

  return (
    <div className="p-6">
      <CategoryList
        onCategoryEdit={handleCategoryEdit}
        onCategoryDelete={handleCategoryDelete}
        onNewCategory={handleNewCategory}
      />
    </div>
  )
}

function ModifiersTabContent() {
  const handleGroupEdit = (groupId: string) => {
    console.log('Edit modifier group:', groupId)
    // TODO: Modal açma işlemi
  }

  const handleGroupDelete = (groupId: string) => {
    console.log('Delete modifier group:', groupId)
    // TODO: Silme onayı ve işlemi
  }

  const handleModifierEdit = (modifierId: string) => {
    console.log('Edit modifier:', modifierId)
    // TODO: Modal açma işlemi
  }

  const handleModifierDelete = (modifierId: string) => {
    console.log('Delete modifier:', modifierId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewGroup = () => {
    console.log('New modifier group')
    // TODO: Yeni grup modal açma
  }

  const handleNewModifier = (groupId: string) => {
    console.log('New modifier for group:', groupId)
    // TODO: Yeni modifiyer modal açma
  }

  return (
    <div className="p-6">
      <ModifierGroupList
        onGroupEdit={handleGroupEdit}
        onGroupDelete={handleGroupDelete}
        onModifierEdit={handleModifierEdit}
        onModifierDelete={handleModifierDelete}
        onNewGroup={handleNewGroup}
        onNewModifier={handleNewModifier}
      />
    </div>
  )
}

function InventoryTabContent() {
  const [activeSubTab, setActiveSubTab] = useState<'items' | 'recipes'>('items')

  const handleItemEdit = (itemId: string) => {
    console.log('Edit inventory item:', itemId)
    // TODO: Modal açma işlemi
  }

  const handleItemDelete = (itemId: string) => {
    console.log('Delete inventory item:', itemId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewItem = () => {
    console.log('New inventory item')
    // TODO: Yeni envanter kalemi modal açma
  }

  const handleRecipeEdit = (recipeId: string) => {
    console.log('Edit recipe:', recipeId)
    // TODO: Modal açma işlemi
  }

  const handleRecipeDelete = (recipeId: string) => {
    console.log('Delete recipe:', recipeId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewRecipe = () => {
    console.log('New recipe')
    // TODO: Yeni reçete modal açma
  }

  return (
    <div className="p-6">
      {/* Sub Tabs */}
      <div className="mb-6">
        <div className="flex gap-2 border-b border-gray-200">
          <Button
            variant={activeSubTab === 'items' ? 'default' : 'ghost'}
            onClick={() => setActiveSubTab('items')}
            className="rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca]"
          >
            Envanter Kalemleri
          </Button>
          <Button
            variant={activeSubTab === 'recipes' ? 'default' : 'ghost'}
            onClick={() => setActiveSubTab('recipes')}
            className="rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca]"
          >
            Reçeteler
          </Button>
        </div>
      </div>

      {/* Content */}
      {activeSubTab === 'items' ? (
        <InventoryItemList
          onItemEdit={handleItemEdit}
          onItemDelete={handleItemDelete}
          onNewItem={handleNewItem}
        />
      ) : (
        <RecipeList
          onRecipeEdit={handleRecipeEdit}
          onRecipeDelete={handleRecipeDelete}
          onNewRecipe={handleNewRecipe}
        />
      )}
    </div>
  )
}

function PricingTabContent() {
  const handleRuleEdit = (ruleId: string) => {
    console.log('Edit pricing rule:', ruleId)
    // TODO: Modal açma işlemi
  }

  const handleRuleDelete = (ruleId: string) => {
    console.log('Delete pricing rule:', ruleId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewRule = () => {
    console.log('New pricing rule')
    // TODO: Yeni kural modal açma
  }

  return (
    <div className="p-6">
      <PricingRuleList
        onRuleEdit={handleRuleEdit}
        onRuleDelete={handleRuleDelete}
        onNewRule={handleNewRule}
      />
    </div>
  )
}
