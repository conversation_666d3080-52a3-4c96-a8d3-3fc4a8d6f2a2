import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../../components/ui/tabs'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { ProductIcon, SearchIcon, PlusIcon } from '../../../assets/icons'
import { ProductList } from '../components/ProductList'

/**
 * <PERSON>rün Yönetimi Ana Sayfası
 * 
 * Bu sayfa:
 * - Figma tasarımına sadık kalarak ürün yönetimi arayüzünü sağlar
 * - 5 sekme iç<PERSON>r: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Fiyatlandırma
 * - Responsive tasarım ve touch-friendly UI
 * - Arama çubuğu ve yeni ekleme butonları
 * - Full viewport coverage (margin/padding sıfır)
 */
export function ProductManagementPage() {
  const [activeTab, setActiveTab] = useState('products')
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="min-h-screen bg-neutral-100 w-full">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 px-6 py-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] rounded-2xl p-3 shadow-lg">
            <ProductIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-[#202325] leading-8">
              Ürün Yönetimi
            </h1>
            <p className="text-sm text-[#636566] leading-5 mt-1">
              Menü öğelerinizi yönetin ve düzenleyin
            </p>
          </div>
        </div>

        {/* Search Bar */}
        <div className="flex justify-end">
          <div className="relative w-80">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <SearchIcon className="w-5 h-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Ürün, kategori veya modifiyer ara..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-12 rounded-2xl border-gray-200 bg-white"
            />
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white border-b border-gray-200">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="h-auto bg-transparent p-0 gap-0 ml-6">
            <TabsTrigger 
              value="products" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm"
            >
              Ürünler
            </TabsTrigger>
            <TabsTrigger 
              value="categories" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Kategoriler
            </TabsTrigger>
            <TabsTrigger 
              value="modifiers" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Modifiyerler
            </TabsTrigger>
            <TabsTrigger 
              value="inventory" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Envanter
            </TabsTrigger>
            <TabsTrigger 
              value="pricing" 
              className="rounded-t-xl rounded-b-none border-b-2 border-transparent data-[state=active]:border-[#025cca] data-[state=active]:bg-[#f0f8ff] data-[state=active]:text-[#025cca] px-4 py-4 h-auto font-semibold text-sm text-[#636566]"
            >
              Fiyatlandırma
            </TabsTrigger>
          </TabsList>

          {/* Tab Contents */}
          <TabsContent value="products" className="mt-0">
            <ProductsTabContent />
          </TabsContent>
          
          <TabsContent value="categories" className="mt-0">
            <CategoriesTabContent />
          </TabsContent>
          
          <TabsContent value="modifiers" className="mt-0">
            <ModifiersTabContent />
          </TabsContent>
          
          <TabsContent value="inventory" className="mt-0">
            <InventoryTabContent />
          </TabsContent>
          
          <TabsContent value="pricing" className="mt-0">
            <PricingTabContent />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

// Placeholder components for tab contents
function ProductsTabContent() {
  const [searchQuery, setSearchQuery] = useState('')

  const handleProductEdit = (productId: string) => {
    console.log('Edit product:', productId)
    // TODO: Modal açma işlemi
  }

  const handleProductDelete = (productId: string) => {
    console.log('Delete product:', productId)
    // TODO: Silme onayı ve işlemi
  }

  const handleNewProduct = () => {
    console.log('New product')
    // TODO: Yeni ürün modal açma
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <Button
          onClick={handleNewProduct}
          className="bg-gradient-to-r from-[#0a4fb8] to-[#025cca] text-white rounded-2xl h-11 px-6 shadow-lg hover:shadow-xl transition-shadow"
        >
          <PlusIcon className="w-5 h-5 mr-2" />
          Yeni Ürün Ekle
        </Button>
      </div>

      <ProductList
        searchQuery={searchQuery}
        onProductEdit={handleProductEdit}
        onProductDelete={handleProductDelete}
      />
    </div>
  )
}

function CategoriesTabContent() {
  return (
    <div className="p-6">
      <div className="text-center py-12 text-gray-500">
        Kategori listesi bileşeni burada olacak
      </div>
    </div>
  )
}

function ModifiersTabContent() {
  return (
    <div className="p-6">
      <div className="text-center py-12 text-gray-500">
        Modifiyer listesi bileşeni burada olacak
      </div>
    </div>
  )
}

function InventoryTabContent() {
  return (
    <div className="p-6">
      <div className="text-center py-12 text-gray-500">
        Envanter listesi bileşeni burada olacak
      </div>
    </div>
  )
}

function PricingTabContent() {
  return (
    <div className="p-6">
      <div className="text-center py-12 text-gray-500">
        Fiyatlandırma listesi bileşeni burada olacak
      </div>
    </div>
  )
}
