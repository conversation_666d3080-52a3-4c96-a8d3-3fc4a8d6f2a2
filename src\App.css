@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 94%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

/* Touch-friendly styles for POS system */
@layer components {
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  .pos-button {
    @apply touch-target rounded-lg font-medium transition-all duration-200 active:scale-95;
  }
  
  .pos-grid {
    @apply grid gap-4 p-4;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .pos-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow;
  }
}

/* Custom scrollbar */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
}

/* Dashboard Grid System - Figma tasarımına uygun 12-column layout */
@layer utilities {
  /* Restaurant Dashboard Grid System */
  .dashboard-container {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: auto 1fr auto;
    gap: 0.5rem;
    min-height: 100vh;
  }

  .dashboard-header {
    grid-column: 1 / -1;
    grid-row: 1;
  }

  .dashboard-sidebar {
    grid-column: 1 / 9;
    grid-row: 2;
  }

  .dashboard-main {
    grid-column: 9 / -1;
    grid-row: 2;
  }

  .dashboard-footer {
    grid-column: 1 / -1;
    grid-row: 3;
  }

  /* Responsive Grid Adjustments */
  @media (max-width: 1024px) {
    .dashboard-container {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto 1fr auto;
    }

    .dashboard-sidebar {
      grid-column: 1;
      grid-row: 2;
    }

    .dashboard-main {
      grid-column: 1;
      grid-row: 3;
    }

    .dashboard-footer {
      grid-row: 4;
    }
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  body {
    font-size: 12px;
    line-height: 1.4;
  }

  .receipt {
    width: 80mm;
    margin: 0;
    padding: 0;
  }
}

