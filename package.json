{"name": "restaurant-pos", "version": "1.0.0", "description": "Modern Restaurant POS System built with Electron, React, and TypeScript", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:electron\" \"npm run dev:react\"", "dev:server": "tsx src/server/index.ts", "dev:electron": "electron-builder install-app-deps && set NODE_ENV=development && electron .", "dev:react": "set CHOKIDAR_USEPOLLING=true && vite", "build": "npm run build:react && npm run build:electron", "build:react": "vite build", "build:electron": "tsc -p tsconfig.electron.json", "dist": "npm run build && electron-builder", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "keywords": ["restaurant", "pos", "electron", "react", "typescript", "prisma", "trpc"], "author": "Restaurant POS Team", "license": "MIT", "devDependencies": {"@tanstack/react-query-devtools": "^4.40.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "tsx": "^4.6.2", "typescript": "^5.3.3", "vite": "^5.0.8"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.7.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^4.40.1", "@tanstack/react-router": "^1.8.4", "@trpc/client": "^10.45.0", "@trpc/react-query": "^10.45.0", "@trpc/server": "^10.45.2", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "decimal.js": "^10.4.3", "electron-store": "^8.1.0", "express": "^4.18.2", "lucide-react": "^0.294.0", "prisma": "^5.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "recharts": "^2.8.0", "sonner": "^1.3.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "build": {"appId": "com.restaurant.pos", "productName": "Restaurant POS", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.business"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}