import React, { useState } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'sonner'
import { trpc, trpcClient } from './lib/trpc-client'
import { PinLoginPage } from './features/auth/pages/PinLoginPage'
import { DashboardPage } from './features/dashboard/pages/DashboardPage'
import { ProductManagementPage } from './features/products/pages/ProductManagementPage'
import { useAuthStore } from './features/auth/stores/authStore'
import './App.css'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 dakika
      retry: (failureCount, error: any) => {
        if (error?.data?.code === 'UNAUTHORIZED' || error?.data?.code === 'FORBIDDEN') {
          return false
        }
        return failureCount < 3
      },
    },
  },
})

function AppContent() {
  const { isAuthenticated, user } = useAuthStore()
  const [currentPage, setCurrentPage] = useState<'dashboard' | 'products'>('dashboard')

  if (!isAuthenticated) {
    return <PinLoginPage />
  }

  // Geçici navigasyon için basit butonlar
  if (currentPage === 'products') {
    return (
      <div>
        <div className="fixed top-4 left-4 z-50">
          <button
            onClick={() => setCurrentPage('dashboard')}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Dashboard'a Dön
          </button>
        </div>
        <ProductManagementPage />
      </div>
    )
  }

  return (
    <div>
      <div className="fixed top-4 right-4 z-50">
        <button
          onClick={() => setCurrentPage('products')}
          className="bg-green-500 text-white px-4 py-2 rounded"
        >
          Ürün Yönetimi
        </button>
      </div>
      <DashboardPage />
    </div>
  )
}

function App() {
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <AppContent />
        <Toaster
          position="top-right"
          richColors
          closeButton
          duration={4000}
        />
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </trpc.Provider>
  )
}

export default App

