import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'sonner'
import { trpc, trpcClient } from './lib/trpc-client'
import { PinLoginPage } from './features/auth/pages/PinLoginPage'
import { DashboardPage } from './features/dashboard/pages/DashboardPage'
import { useAuthStore } from './features/auth/stores/authStore'
import './App.css'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 dakika
      retry: (failureCount, error: any) => {
        if (error?.data?.code === 'UNAUTHORIZED' || error?.data?.code === 'FORBIDDEN') {
          return false
        }
        return failureCount < 3
      },
    },
  },
})

function AppContent() {
  const { isAuthenticated, user } = useAuthStore()

  if (!isAuthenticated) {
    return <PinLoginPage />
  }

  return <DashboardPage />
}

function App() {
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <AppContent />
        <Toaster
          position="top-right"
          richColors
          closeButton
          duration={4000}
        />
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </trpc.Provider>
  )
}

export default App

