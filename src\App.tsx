import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { RouterProvider } from '@tanstack/react-router'
import { Toaster } from 'sonner'
import { trpc, trpcClient } from './lib/trpc-client'
import { router } from './router'
import './App.css'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 dakika
      retry: (failureCount, error: any) => {
        if (error?.data?.code === 'UNAUTHORIZED' || error?.data?.code === 'FORBIDDEN') {
          return false
        }
        return failureCount < 3
      },
    },
  },
})

function AppContent() {
  console.log('AppContent rendering...')

  // Geçici olarak router'ı devre dışı bırak ve basit bir test component render et
  return (
    <div className="min-h-screen flex items-center justify-center bg-blue-100">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-blue-800 mb-4">ATROPOS POS</h1>
        <p className="text-blue-600 mb-4">Uygulama başarıyla yüklendi!</p>
        <div className="bg-white p-4 rounded-lg shadow-lg">
          <p className="text-gray-700">React ve tRPC çalışıyor ✅</p>
          <p className="text-gray-700">TanStack Query hazır ✅</p>
          <p className="text-gray-700">Tailwind CSS aktif ✅</p>
        </div>
      </div>
    </div>
  )

  // Router'ı tekrar aktif etmek için aşağıdaki satırları uncomment et:
  // try {
  //   return <RouterProvider router={router} />
  // } catch (error) {
  //   console.error('Router error:', error)
  //   return (
  //     <div className="min-h-screen flex items-center justify-center bg-red-100">
  //       <div className="text-center">
  //         <h1 className="text-2xl font-bold text-red-800 mb-4">Router Hatası</h1>
  //         <p className="text-red-600">{String(error)}</p>
  //       </div>
  //     </div>
  //   )
  // }
}

function App() {
  console.log('App rendering...')
  console.log('tRPC client:', trpcClient)
  console.log('Query client:', queryClient)

  try {
    return (
      <trpc.Provider client={trpcClient} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          <AppContent />
          <Toaster
            position="top-right"
            richColors
            closeButton
            duration={4000}
          />
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </trpc.Provider>
    )
  } catch (error) {
    console.error('App error:', error)
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-100">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-800 mb-4">Uygulama Hatası</h1>
          <p className="text-red-600">{String(error)}</p>
        </div>
      </div>
    )
  }
}

export default App

