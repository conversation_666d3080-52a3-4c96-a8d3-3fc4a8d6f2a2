import React from 'react'

interface MoreModalProps {
  isOpen: boolean
  onClose: () => void
}

interface MenuCardProps {
  icon: React.ReactNode
  title: string
  onClick: () => void
}

function MenuCard({ icon, title, onClick }: MenuCardProps) {
  return (
    <button
      onClick={onClick}
      className="bg-white relative rounded-2xl w-[200px] hover:shadow-lg transition-shadow group"
    >
      <div className="box-border flex flex-col gap-3 items-start justify-start overflow-hidden px-6 py-4 relative w-[200px]">
        <div className="bg-[#f0f8ff] overflow-hidden relative rounded-2xl shrink-0 w-[51px] h-[51px] flex items-center justify-center group-hover:bg-[#e6f3ff] transition-colors">
          {icon}
        </div>
        <div className="font-medium text-[16px] leading-[1.5] text-[#202325] text-left min-w-full">
          {title}
        </div>
      </div>
      <div className="absolute border border-neutral-100 border-solid inset-0 pointer-events-none rounded-2xl shadow-[0px_4px_20px_0px_rgba(0,0,0,0.05)]" />
    </button>
  )
}

export function MoreModal({ isOpen, onClose }: MoreModalProps) {
  if (!isOpen) return null

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleMenuClick = (menuName: string) => {
    console.log(`${menuName} menüsüne tıklandı`)
    // TODO: Routing eklenecek
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50"
      onClick={handleOverlayClick}
    >
      <div className="bg-white rounded-2xl w-[941px] max-w-[90vw] max-h-[90vh] overflow-y-auto">
        <div className="flex flex-col gap-5 items-start justify-start pb-7 pt-6 px-6">
          {/* Title Bar */}
          <div className="flex flex-row h-11 items-center justify-between p-0 w-full">
            <div className="font-semibold text-[24px] leading-[1.5] text-[#202325]">
              Daha Fazla
            </div>
            <button
              onClick={onClose}
              className="bg-neutral-100 overflow-hidden rounded-full w-10 h-10 flex items-center justify-center hover:bg-neutral-200 transition-colors"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15 5L5 15M5 5L15 15"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>

          {/* Tools Section */}
          <div className="flex flex-col gap-3.5 items-start justify-start">
            <div className="font-semibold text-[14px] leading-[1.5] text-[#636566]">
              Araçlar
            </div>
            <div className="flex flex-row gap-4 items-start justify-start flex-wrap">
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M3 21L12 12L21 21H3ZM12 2L22 12H17V20H7V12H2L12 2Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Şube Bilgileri"
                onClick={() => handleMenuClick('Şube Bilgileri')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM7 7H17V9H7V7ZM7 11H17V13H7V11ZM7 15H13V17H7V15Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Rapor Özeti"
                onClick={() => handleMenuClick('Rapor Özeti')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M20 7H16V5L14 3H10L8 5V7H4C2.9 7 2 7.9 2 9V19C2 20.1 2.9 21 4 21H20C21.1 21 22 20.1 22 19V9C22 7.9 21.1 7 20 7ZM10 5H14V7H10V5ZM20 19H4V9H8V11H16V9H20V19Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Envanter"
                onClick={() => handleMenuClick('Envanter')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12.79 21L3 11.21V2H11.21L21 11.79L12.79 21ZM5.41 4L4 5.41L5.41 6.83L6.83 5.41L5.41 4ZM12.79 18.17L18.17 12.79L9.21 3.83L3.83 9.21L12.79 18.17Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="İndirim"
                onClick={() => handleMenuClick('İndirim')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M18 3H6C4.9 3 4 3.9 4 5V19C4 20.1 4.9 21 6 21H18C19.1 21 20 20.1 20 19V5C20 3.9 19.1 3 18 3ZM18 19H6V5H18V19ZM8 7H16V9H8V7ZM8 11H16V13H8V11ZM8 15H12V17H8V15Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Yazıcı"
                onClick={() => handleMenuClick('Yazıcı')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Dil"
                onClick={() => handleMenuClick('Dil')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2ZM4 14L5 17H7L6 14H4ZM9 14L10 17H12L11 14H9ZM14 14L15 17H17L16 14H14ZM19 14L20 17H22L21 14H19Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Ürünler Yönetimi"
                onClick={() => handleMenuClick('Ürünler Yönetimi')}
              />
            </div>
          </div>

          {/* Help Section */}
          <div className="flex flex-col gap-3.5 items-start justify-start">
            <div className="font-semibold text-[14px] leading-[1.5] text-[#636566]">
              Yardım
            </div>
            <div className="flex flex-row gap-4 items-start justify-start flex-wrap">
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 19H11V17H13V19ZM15.07 11.25L14.17 12.17C13.45 12.9 13 13.5 13 15H11V14.5C11 13.4 11.45 12.4 12.17 11.67L13.41 10.41C13.78 10.05 14 9.55 14 9C14 7.9 13.1 7 12 7C10.9 7 10 7.9 10 9H8C8 6.79 9.79 5 12 5C14.21 5 16 6.79 16 9C16 9.88 15.64 10.68 15.07 11.25Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Yardım Merkezi"
                onClick={() => handleMenuClick('Yardım Merkezi')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 7C13.1 7 14 7.9 14 9C14 10.1 13.1 11 12 11C10.9 11 10 10.1 10 9C10 7.9 10.9 7 12 7ZM12 17C10.33 17 8.94 16.16 8.24 14.9C8.26 13.58 11 12.9 12 12.9C13 12.9 15.74 13.58 15.76 14.9C15.06 16.16 13.67 17 12 17Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Gizlilik Politikası"
                onClick={() => handleMenuClick('Gizlilik Politikası')}
              />
              <MenuCard
                icon={
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path
                      d="M13 9H11V7H13M13 17H11V11H13M12 2A10 10 0 0 0 2 12A10 10 0 0 0 12 22A10 10 0 0 0 22 12A10 10 0 0 0 12 2Z"
                      fill="#025CCA"
                    />
                  </svg>
                }
                title="Uygulama Bilgileri"
                onClick={() => handleMenuClick('Uygulama Bilgileri')}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
